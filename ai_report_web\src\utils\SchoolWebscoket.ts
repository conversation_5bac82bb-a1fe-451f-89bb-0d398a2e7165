import {useUserStore} from "@/store/modules/user"
const userStore = useUserStore()
interface requestData{
    report_id:string;
    school_name:string;
    college_name:string;
    module_type:string;
}
type EventHandler = (data:any)=>void
type ErrorHandler = (error:any)=>void;
class SchoolWebSocket {
    private socket:WebSocket|null = null;
    private url:string="";
    private heartbeatInterval:number = 1000;
    private heartbeatTimer:ReturnType<typeof setInterval>|null = null;
    private connectionTimeout:number|null = null;
    private reconnectInterval:number = 6000;
    private reconnectTimer:ReturnType<typeof setInterval>|null = null;
    private messageHandler:EventHandler|null=null;
    private openHandler:EventHandler|null=null;
    private errorHandler:ErrorHandler|null=null;
    private connectionHandler:EventHandler|null=null;
    private disconnectionHandler:EventHandler|null=null;
    private token:string="";

    constructor(url:string,heartbeatInterval:string, reconnectInterval:string, token:string) {
        this.url = url;
        this.heartbeatInterval = parseInt(heartbeatInterval);
        this.reconnectInterval = parseInt(reconnectInterval);
        this.token = token;
    }
    public connect() {
        this.socket = new WebSocket(this.url);
        this.socket.onopen = (event) => {
            console.log("socket 连接成功")
            this.openHandler?.(event);
            this.sendHeartbeat()
        };
        this.socket.onmessage = (event) => {
            console.log("socket 收到消息",event)
            this.messageHandler?.(event);
        };
        this.socket.onerror = (event) => {
            console.log("socket 连接错误",event)
            this.stopHeartbeat()
            this.errorHandler?.(event);
        };
        this.socket.onclose = (event) => {
            console.log("socket 连接关闭",event)
            this.stopHeartbeat()
            this.disconnectionHandler?.(event);
            this.reconnect()
        };
    }
    public send(data:requestData){
        if(this.socket && this.socket.readyState===WebSocket.OPEN){
            //token 带上
            this.socket.send(JSON.stringify({...data, token:this.token}));
        }else{
            console.log("当前socket未连接")
        }
    }
    public onopen(callback:EventHandler) {
        this.openHandler = callback;
    }
    public onmessage(callback:EventHandler) {
        this.messageHandler = callback;
    }
    public onerror(callback:ErrorHandler) {
        this.errorHandler = callback;
    }
    public onclose(callback:EventHandler) {
        this.disconnectionHandler = callback;
    }
    //心跳
    public startHeartbeat(){
        this.heartbeatTimer = setInterval(() => {
            this.sendHeartbeat();
        }, this.heartbeatInterval);
    }

    public sendHeartbeat(){
        if(this.socket){
            this.socket.send("ping");
        }
    }

    public stopHeartbeat(){
        if(this.heartbeatTimer){
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }

    public reconnect(){
        if(this.reconnectTimer) return
        this.reconnectTimer = setTimeout(() => {
            this.connect();
            this.reconnectTimer = null;
        }, this.reconnectInterval);
    }

}

// 

const task = (data:requestData, callback:(data:any)=>void) => {
    return new Promise((resolve, reject) => {
        const socket = new SchoolWebSocket('ws://127.0.0.1:8788', '1000', '6000', userStore.token);
        socket.connect();
        socket.onopen(() => {
            console.log('连接成功');
            socket.send(data);
        });
        socket.onmessage((event) => {
            console.log('收到消息', event.data);
            callback(event.data);
        });

    })
}